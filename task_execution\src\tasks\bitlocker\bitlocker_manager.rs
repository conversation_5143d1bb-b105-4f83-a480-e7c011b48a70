use logger::debug;
use std::collections::{BTreeMap, HashMap};
use wmi::{COMLibrary, Variant, WMIConnection};

#[derive(Default, Debug, Clone)]
pub struct DriveInformation {
    pub device_id: String,
    pub drive_letter: String,
    pub persistent_volume_id: String,
    pub protection_status: i32,
    pub encryption_method: i32,
    pub version: i32,
    pub lock_status: i32,
    pub conversion_status: i32,
    pub encryption_percentage: i32,
    pub encryption_flags: i32,
    pub wiping_status: i32,
    pub wiping_percentage: i32,
    pub drive_format: String,
    pub drive_size: String,
    pub drive_size_in_bytes: i64,
    pub is_autounlock_enabled: bool,
    pub autounlock_key_protector_id: String,
    pub available_key_protectors: Vec<u32>,
    pub drive_type: u32,
    pub dip: String,
    pub volume_type: i32,
}

pub struct BitlockerHandler {
    drive_count: u32,
    drives: HashMap<String, DriveInformation>,
    wmi: WMIConnection,
}

impl BitlockerHandler {
    pub fn init() -> anyhow::Result<Self> {
        let library = COMLibrary::new()?;
        let connection = wmi::WMIConnection::with_namespace_path(
            "ROOT\\CIMV2\\Security\\MicrosoftVolumeEncryption",
            library,
        )?;

        let query = "SELECT * FROM Win32_encryptablevolume";
        let result: Vec<BTreeMap<String, Variant>> = connection.raw_query(query)?;

        let mut drives = HashMap::new();
        let drive_count = result.len() as u32;

        for drive in result {
            let device_id = drive
                .get("DeviceID")
                .and_then(|v| match v {
                    Variant::String(s) => Some(s.clone()),
                    _ => None,
                })
                .unwrap_or_default();

            let drive_letter = drive
                .get("DriveLetter")
                .and_then(|v| match v {
                    Variant::String(s) => Some(s.clone()),
                    _ => None,
                })
                .unwrap_or_default();

            let drive_detail = DriveInformation {
                device_id: device_id.clone(),
                drive_letter,
                ..Default::default()
            };
            drives.insert(device_id, drive_detail);
        }

        debug!("Found {} drives", drive_count);
        Ok(Self {
            drive_count,
            drives,
            wmi: connection,
        })
    }
}
