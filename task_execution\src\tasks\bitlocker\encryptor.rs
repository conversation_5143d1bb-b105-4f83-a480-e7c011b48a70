use crate::tasks::bitlocker::encryption_volume_status::EncryptionVolumeStatus;
use anyhow::Result;
use database::models::{BitlockerPolicy, DriveEncryptionType, EncryptionMethod, TpmProtectionMode};
use logger::debug;
use windows::{
    core::{BSTR, HSTRING, <PERSON><PERSON><PERSON>},
    Wdk::System::SystemServices::RtlGetVersion,
    Win32::{
        Storage::FileSystem::{GetDriveTypeW, GetLogicalDriveStringsW},
        System::{
            Com::{
                CoCreateInstance, CoInitializeEx, CoInitializeSecurity, CoSetProxyBlanket,
                CoUninitialize, CLSCTX_INPROC_SERVER, COINIT_MULTITHREADED, EOAC_NONE,
                RPC_C_AUTHN_LEVEL_CALL, RPC_C_AUTHN_LEVEL_DEFAULT, RPC_C_IMP_LEVEL_IMPERSONATE,
            },
            Ole::SysAllocString,
            SystemInformation::OSVERSIONINFOW,
            TpmBaseServices::Tbsi_Is_Tpm_Present,
            Variant::{VARENUM, VARIANT, VT_BSTR},
            WindowsProgramming::{DRIVE_FIXED, DRIVE_REMOVABLE},
            Wmi::{
                IWbemClassObject, IWbemLocator, IWbemServices, WbemLocator, WBEM_FLAG_FORWARD_ONLY,
                WBEM_FLAG_RETURN_IMMEDIATELY, WBEM_GENERIC_FLAG_TYPE, WBEM_INFINITE,
            },
        },
    },
};

fn make_bstr_variant(s: &str) -> VARIANT {
    unsafe {
        let mut v = VARIANT::default();
        *v.Anonymous.Anonymous.vt = VARENUM(VT_BSTR.0 as u16);
        *v.Anonymous.Anonymous.Anonymous.bstrVal = SysAllocString(&HSTRING::from(s));
        v
    }
}

#[derive(Debug, Default)]
struct DiskDrives {
    os_drive: String,
    fixed_drives: Vec<String>,
    removable_drives: Vec<String>,
}

impl DiskDrives {
    pub fn detect() -> Result<Self> {
        let mut buffer = [0u16; 256];
        unsafe {
            GetLogicalDriveStringsW(Some(&mut buffer));
        }

        let drives: Vec<String> = buffer
            .split(|c| *c == 0)
            .filter(|s| !s.is_empty())
            .map(String::from_utf16_lossy)
            .collect();

        let mut result = Self::default();

        for drive in drives {
            let drive_chars: Vec<u16> = drive.encode_utf16().chain([0]).collect();
            let drive_type = unsafe { GetDriveTypeW(PCWSTR(drive_chars.as_ptr())) };

            match drive_type {
                DRIVE_FIXED => result
                    .fixed_drives
                    .push(drive.trim_end_matches('\\').to_string()),
                DRIVE_REMOVABLE => result
                    .removable_drives
                    .push(drive.trim_end_matches('\\').to_string()),
                _ => {}
            }

            // Detect OS drive
            if Self::is_os_drive(&drive) {
                result.os_drive = drive.trim_end_matches('\\').to_string();
            }
        }

        debug!("Detected drives: {:?}", result);

        Ok(result)
    }

    fn is_os_drive(drive: &str) -> bool {
        let system_root = std::env::var("SystemRoot").unwrap_or("C:\\Windows".to_string());
        system_root.starts_with(drive)
    }
}

pub struct Encryptor<'a> {
    policy: &'a BitlockerPolicy,
    has_tpm: bool,
    volume_status: Vec<EncryptionVolumeStatus>,
    os_version: String,
    disks: DiskDrives,
    pin: Option<String>,
    password: Option<String>,
}

impl<'a> Encryptor<'a> {
    pub fn new(
        policy: &'a BitlockerPolicy,
        pin: Option<String>,
        password: Option<String>,
    ) -> Result<Self> {
        Ok(Self {
            policy,
            has_tpm: Self::detect_tpm()?,
            os_version: Self::os_version(),
            disks: DiskDrives::detect()?,
            volume_status: EncryptionVolumeStatus::get_all()?,
            pin,
            password,
        })
    }

    fn detect_tpm() -> Result<bool> {
        let result = unsafe { Tbsi_Is_Tpm_Present() };
        debug!("TPM present: {}", result.as_bool());
        Ok(result.as_bool())
    }

    fn os_version() -> String {
        unsafe {
            let mut info = OSVERSIONINFOW {
                dwOSVersionInfoSize: std::mem::size_of::<OSVERSIONINFOW>() as u32,
                ..Default::default()
            };

            let status = RtlGetVersion(&mut info);

            let version = if status.is_ok() {
                match (info.dwMajorVersion, info.dwMinorVersion) {
                    (10, 0) => {
                        if info.dwBuildNumber >= 22000 {
                            "Win11"
                        } else {
                            "Win10"
                        }
                    }
                    (6, 3) => "Win8.1",
                    (6, 2) => "Win8",
                    (6, 1) => "Win7",
                    _ => "Unknown Windows version",
                }
            } else {
                "Unknown Windows version"
            }
            .to_string();

            debug!("OS version: {}", version);

            version
        }
    }

    pub fn execute(&self) -> Result<()> {
        let encryption_method = match self.os_version.as_str() {
            "win7" => &self.policy.encryption_method_win7,
            "win8" | "win8.1" => &self.policy.encryption_method_win8,
            "win10" | "win11" => &self.policy.encryption_method_win10,
            _ => &self.policy.encryption_method_win10,
        };

        // 4. OS DRIVE encryption
        if self.policy.encrypt_os_drive {
            self.encrypt_drive(
                &self.disks.os_drive,
                true,
                &encryption_method,
                &self.policy.os_drive_encryption_type,
            )?;
        }

        // 5. Fixed drives
        if self.policy.encrypt_fixed_drives {
            for disk in self.disks.fixed_drives.iter() {
                if &self.disks.os_drive == disk {
                    continue;
                }
                self.encrypt_drive(
                    &disk,
                    false,
                    &encryption_method,
                    &self.policy.fixed_drive_encryption_type,
                )?;
            }
        }

        // 6. Removable drives
        if self.policy.encrypt_removable_drives {
            for disk in self.disks.removable_drives.iter() {
                self.encrypt_drive(
                    &disk,
                    false,
                    &encryption_method,
                    &self.policy.removable_drive_encryption_type,
                )?;
            }
        }

        Ok(())
    }

    fn encrypt_drive(
        &self,
        drive: &str,
        is_os_drive: bool,
        encryption_method: &EncryptionMethod,
        encryption_type: &DriveEncryptionType,
    ) -> Result<()> {
        let encryption_cmd = encryption_method.to_command();

        if is_os_drive {
            // TPM + PIN logic
            if self.has_tpm == false && self.policy.allow_without_tpm == false {
                anyhow::bail!("TPM is not available and policy forbids encryption without TPM");
            }
            self.add_protector(drive)?;
        } else {
            self.add_protector(drive)?;
        }

        let encryption_command = format!(
            "manage-bde.exe -on {} -EncryptionMethod {}{}",
            drive,
            encryption_cmd,
            if encryption_type == &DriveEncryptionType::UsedSpaceOnly {
                " -UsedSpaceOnly"
            } else {
                ""
            }
        );

        debug!("Built command to encrypt drive: {}", encryption_command);

        Ok(())
    }

    fn add_protector(&self, drive: &str) -> Result<()> {
        unsafe {
            CoInitializeEx(None, COINIT_MULTITHREADED).ok()?;

            CoInitializeSecurity(
                None,
                -1,
                None,
                None,
                RPC_C_AUTHN_LEVEL_DEFAULT,
                RPC_C_IMP_LEVEL_IMPERSONATE,
                None,
                EOAC_NONE,
                None,
            )
            .ok()
            .ok_or_else(|| anyhow::anyhow!("Failed to initialize COM security"))?;

            // Create WMI locator
            let locator: IWbemLocator = CoCreateInstance(&WbemLocator, None, CLSCTX_INPROC_SERVER)?;

            // Connect to BitLocker namespace
            let services: IWbemServices = locator.ConnectServer(
                &BSTR::from("ROOT\\CIMV2\\Security\\MicrosoftVolumeEncryption"),
                &BSTR::new(),
                &BSTR::new(),
                &BSTR::new(),
                0,
                &BSTR::new(),
                None,
            )?;

            // Impersonation
            CoSetProxyBlanket(
                &services,
                RPC_C_AUTHN_LEVEL_DEFAULT.0,
                RPC_C_AUTHN_LEVEL_DEFAULT.0,
                None,
                RPC_C_AUTHN_LEVEL_CALL,
                RPC_C_IMP_LEVEL_IMPERSONATE,
                None,
                EOAC_NONE,
            )
            .ok()
            .ok_or_else(|| anyhow::anyhow!("Failed to set proxy blanket"))?;

            // Query volume based on drive parameter
            let query = format!(
                "SELECT * FROM Win32_EncryptableVolume WHERE DriveLetter='{}'",
                drive
            );
            let enumerator = services.ExecQuery(
                &BSTR::from("WQL"),
                &BSTR::from(query),
                WBEM_FLAG_FORWARD_ONLY | WBEM_FLAG_RETURN_IMMEDIATELY,
                None,
            )?;

            let mut obj = [None; 1];
            let mut returned = 0u32;
            enumerator
                .Next(WBEM_INFINITE, &mut obj, &mut returned)
                .ok()?;

            if returned == 0 {
                anyhow::bail!("No encryptable volume found for drive {}", drive);
            }

            let volume: IWbemClassObject = obj[0].as_ref().unwrap().clone();

            // Get input param class
            let mut in_class = None;
            volume.GetMethod(
                &BSTR::from("ProtectKeyWithPassphrase"),
                0,
                &mut in_class,
                std::ptr::null_mut(),
            )?;
            let in_class = in_class.unwrap();

            // Spawn instance
            let in_inst = in_class.SpawnInstance(0)?;

            // Set password - use the actual password from the encryptor
            let password = self
                .password
                .as_ref()
                .map_or("DefaultPassword123!".to_string(), |p| p.clone());
            let variant_pass = make_bstr_variant(&password);
            in_inst.Put(&BSTR::from("Passphrase"), 0, &variant_pass, 0)?;

            // Call the method
            let object_path = format!("Win32_EncryptableVolume.DriveLetter=\"{}\"", drive);
            let mut out_params = None;
            services.ExecMethod(
                &BSTR::from(object_path),
                &BSTR::from("ProtectKeyWithPassphrase"),
                WBEM_GENERIC_FLAG_TYPE(0),
                None,
                &in_inst,
                Some(&mut out_params),
                None,
            )?;

            // Check return code
            let mut ret = VARIANT::default();
            out_params
                .unwrap()
                .Get(&BSTR::from("ReturnValue"), 0, &mut ret, None, None)?;

            let return_value = unsafe { ret.Anonymous.Anonymous.Anonymous.ulVal };
            debug!(
                "Password protector added with return value: {}",
                return_value
            );

            if return_value != 0 {
                anyhow::bail!(
                    "Failed to add password protector, return code: {}",
                    return_value
                );
            }

            CoUninitialize();
        }

        Ok(())
    }
    //     TpmProtectionMode::TpmOnly => {
    //         if self.has_tpm == false {
    //             if self.password.is_none() {
    //                 anyhow::bail!("Password is required for TPM-less protection mode");
    //             } else {
    //                 debug!("Password is available for TPM-less protection mode");
    //             }
    //         } else {
    //             debug!("TPM is available for TPM-only protection mode");
    //         }
    //     }
    //     TpmProtectionMode::TpmAndPin => {
    //         if self.pin.is_none() {
    //             anyhow::bail!("PIN is required for TPM and PIN protection mode");
    //         }
    //         debug!("PIN is available for TPM and PIN protection mode");
    //     }
    // }
    // Ok(())
}
