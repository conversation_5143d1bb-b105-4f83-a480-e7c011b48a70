[package]
name = "playground"
description.workspace = true
edition.workspace = true
version.workspace = true
authors.workspace = true

[dependencies]
task_execution = { path = "../task_execution" }
database = { path = "../database" }
# self_service = { path = "../self_service" }
# ipc = { path = "../ipc" }
# api = { path = "../api" }
# database = { path = "../database" }
# fim = { path = "../fim" }
# data_collection = { path = "../data_collection" }
logger = { path = "../logger" }
# agents = { path = "../agents" }
utils = { path = "../utils", features = ["self_service"] }
tokio = { workspace = true }
# tauri = { workspace = true }
# encoding_rs_io = "0.1.7"
# encoding_rs = "0.8.35"
serde_json = { workspace = true }
# rayon = { workspace = true }
# glob = "0.3.2"
# walkdir = "2.5.0"
# ignore = "0.4.23"
# globset = "0.4.16"
# sysinfo = { workspace = true }
# shell = { path = "../shell" }
