use crate::{has_task::HasTask, log_task::LogTask, sync_task::SyncTask, TaskExecutable};
use anyhow::{anyhow, Result};
use async_trait::async_trait;
use database::{
    data_types::TaskResult,
    models::{BitlockerPolicy, Task, TaskStatus},
};
use logger::ModuleLogger;
use std::sync::Arc;

#[derive(Debug)]
pub struct BitlockerEncryptionTask {
    task: Task,
    logger: Arc<ModuleLogger>,
}

impl BitlockerEncryptionTask {
    pub fn new(task: Task) -> Self {
        Self {
            task,
            logger: ModuleLogger::new("bitlocker", None, Some("bitlocker_encryption".to_owned())),
        }
    }

    async fn get_bitlocker_policy(&self) -> Result<&BitlockerPolicy> {
        self.task
            .bitlocker_policy
            .as_ref()
            .ok_or_else(|| anyhow!("No BitLocker policy found in task"))
    }

    async fn apply_bitlocker_policy(&self, policy: &BitlockerPolicy) -> Result<TaskResult> {
        use crate::tasks::bitlocker::encryptor::Encryptor;

        self.write_task_log(
            format!("Applying BitLocker policy: {}", policy.display_name),
            None,
        )
        .await;

        let encryptor = Encryptor::new(
            policy,
            Some("TempPin".to_owned()),
            Some("TempPassword".to_owned()),
        )?;

        encryptor.execute()?;

        Ok(TaskResult {
            exit_code: 0,
            status: TaskStatus::Success,
            output: "BitLocker encryption completed successfully".to_string(),
        })
    }
}

#[async_trait]
impl TaskExecutable for BitlockerEncryptionTask {
    async fn execute(&mut self) -> Result<TaskResult> {
        self.write_task_log(
            format!(
                "Initiating execution of BitLocker encryption task {}",
                self.get_name()
            ),
            None,
        )
        .await;

        // #[cfg(not(windows))]
        // {
        //     self.write_task_log(
        //         "BitLocker encryption task is not supported on this platform".to_string(),
        //         Some("ERROR"),
        //     )
        //     .await;
        //     return Ok(TaskResult {
        //         exit_code: 1,
        //         status: TaskStatus::Failed,
        //         output: "BitLocker encryption task is not supported on this platform".to_string(),
        //     });
        // }

        // // Get the BitLocker policy from the task
        // #[allow(unreachable_code)]
        let policy = match self.get_bitlocker_policy().await {
            Ok(policy) => policy,
            Err(error) => {
                let error_msg = format!("Failed to get BitLocker policy: {:?}", error);
                self.write_task_log(error_msg.clone(), Some("ERROR")).await;
                return Ok(TaskResult {
                    exit_code: 1,
                    status: TaskStatus::Failed,
                    output: error_msg,
                });
            }
        };

        // Apply the BitLocker policy
        match self.apply_bitlocker_policy(policy).await {
            Ok(result) => {
                self.write_task_log(
                    format!(
                        "BitLocker encryption task completed with status: {}",
                        result.status
                    ),
                    None,
                )
                .await;
                Ok(result)
            }
            Err(error) => {
                let error_msg = format!("BitLocker encryption task failed: {:?}", error);
                self.write_task_log(error_msg.clone(), Some("ERROR")).await;
                Ok(TaskResult {
                    exit_code: 1,
                    status: TaskStatus::Failed,
                    output: error_msg,
                })
            }
        }
    }
}

impl HasTask for BitlockerEncryptionTask {
    fn get_task(&self) -> &Task {
        &self.task
    }

    fn set_task(&mut self, task: Task) {
        self.task = task;
    }
}

impl LogTask for BitlockerEncryptionTask {
    fn logger(&self) -> Arc<ModuleLogger> {
        Arc::clone(&self.logger)
    }
}

impl SyncTask for BitlockerEncryptionTask {}
