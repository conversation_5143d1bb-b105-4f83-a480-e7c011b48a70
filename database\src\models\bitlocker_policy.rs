use crate::{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>};
use logger::error;
use serde::{Deserialize, Serialize};
use serde_json::Value;
use std::fmt::Display;

#[derive(PartialEq, Eq, Serialize, Deserialize, Clone, Debug, Default)]
pub enum GlobalEncryptionType {
    #[serde(rename = "Software")]
    #[default]
    Software,
    #[serde(rename = "Hardware")]
    Hardware,
    #[serde(rename = "HardwareAndSoftware")]
    HardwareAndSoftware,
}

impl Display for GlobalEncryptionType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            GlobalEncryptionType::Software => f.write_str("Software"),
            GlobalEncryptionType::Hardware => f.write_str("Hardware"),
            GlobalEncryptionType::HardwareAndSoftware => f.write_str("HardwareAndSoftware"),
        }
    }
}

#[derive(<PERSON><PERSON><PERSON>q, <PERSON>q, Serialize, Deserialize, <PERSON><PERSON>, Debug, Default)]
pub enum EncryptionMethod {
    #[serde(rename = "Aes128")]
    #[default]
    Aes128,
    #[serde(rename = "Aes256")]
    Aes256,
    #[serde(rename = "XtsAes128")]
    XtsAes128,
    #[serde(rename = "XtsAes256")]
    XtsAes256,
    #[serde(rename = "AesCbc128")]
    AesCbc128,
    #[serde(rename = "AesCbc256")]
    AesCbc256,
    #[serde(rename = "AesXts128")]
    AesXts128,
    #[serde(rename = "AesXts256")]
    AesXts256,
}

impl EncryptionMethod {
    pub fn to_command(&self) -> &str {
        match self {
            &EncryptionMethod::Aes128 => "AES128",
            &EncryptionMethod::Aes256 => "AES256",
            &EncryptionMethod::XtsAes128 => "XTS_AES128",
            &EncryptionMethod::XtsAes256 => "XTS_AES256",
            _ => "XTS_AES256",
        }
    }
}

impl Display for EncryptionMethod {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            EncryptionMethod::Aes128 => f.write_str("Aes128"),
            EncryptionMethod::Aes256 => f.write_str("Aes256"),
            EncryptionMethod::XtsAes128 => f.write_str("XtsAes128"),
            EncryptionMethod::XtsAes256 => f.write_str("XtsAes256"),
            EncryptionMethod::AesCbc128 => f.write_str("AesCbc128"),
            EncryptionMethod::AesCbc256 => f.write_str("AesCbc256"),
            EncryptionMethod::AesXts128 => f.write_str("AesXts128"),
            EncryptionMethod::AesXts256 => f.write_str("AesXts256"),
        }
    }
}

#[derive(PartialEq, Eq, Serialize, Deserialize, Clone, Debug, Default)]
pub enum DriveEncryptionType {
    #[serde(rename = "FullVolume")]
    #[default]
    FullVolume,
    #[serde(rename = "UsedSpaceOnly")]
    UsedSpaceOnly,
}

impl Display for DriveEncryptionType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            DriveEncryptionType::FullVolume => f.write_str("FullVolume"),
            DriveEncryptionType::UsedSpaceOnly => f.write_str("UsedSpaceOnly"),
        }
    }
}

#[derive(PartialEq, Eq, Serialize, Deserialize, Clone, Debug, Default)]
pub enum TpmProtectionMode {
    #[serde(rename = "TpmOnly")]
    #[default]
    TpmOnly,
    #[serde(rename = "TpmAndPin")]
    TpmAndPin,
}

impl Display for TpmProtectionMode {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            TpmProtectionMode::TpmOnly => f.write_str("TpmOnly"),
            TpmProtectionMode::TpmAndPin => f.write_str("TpmAndPin"),
        }
    }
}

#[derive(Debug, Serialize, Deserialize, Clone, Default)]
#[serde(rename_all = "camelCase")]
pub struct BitlockerPolicy {
    pub id: PrimaryKey,
    pub name: String,
    pub display_name: String,
    pub description: String,
    pub enabled: bool,
    pub global_encryption_type: GlobalEncryptionType,
    pub encryption_method_win7: EncryptionMethod,
    pub encryption_method_win8: EncryptionMethod,
    pub encryption_method_win10: EncryptionMethod,
    pub encryption_method_server: EncryptionMethod,
    #[serde(alias = "encryptOSDrive")]
    pub encrypt_os_drive: bool,
    pub os_drive_encryption_type: DriveEncryptionType,
    pub tpm_protection_mode: TpmProtectionMode,
    #[serde(alias = "minimumPINLength")]
    pub minimum_pin_length: u32,
    #[serde(alias = "enableEnhancedPIN")]
    pub enable_enhanced_pin: bool,
    pub enforce_minimum_complexity: bool,
    #[serde(alias = "allowWithoutTPM")]
    pub allow_without_tpm: bool,
    pub minimum_password_length: u32,
    pub encrypt_fixed_drives: bool,
    pub fixed_drive_encryption_type: DriveEncryptionType,
    pub encrypt_removable_drives: bool,
    pub removable_drive_encryption_type: DriveEncryptionType,
    pub recovery_key_rotation_enabled: bool,
    pub recovery_key_rotation_period: u32,
    #[serde(alias = "saveRecoveryKeyToAD")]
    pub save_recovery_key_to_ad: bool,
}

impl HasPrimaryKey for BitlockerPolicy {
    fn table_name(&self) -> &str {
        "bitlocker_policy"
    }

    fn get_primary_key(&self) -> &PrimaryKey {
        &self.id
    }
}

impl From<Value> for BitlockerPolicy {
    fn from(value: Value) -> Self {
        match serde_json::from_value::<Self>(value) {
            Ok(data) => data,
            Err(error) => {
                error!(?error, "Failed to convert Value into BitlockerPolicy");
                BitlockerPolicy::default()
            }
        }
    }
}
