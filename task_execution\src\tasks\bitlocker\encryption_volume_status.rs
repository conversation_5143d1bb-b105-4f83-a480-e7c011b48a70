use anyhow::Result;
use logger::debug;

#[derive(Debug, <PERSON><PERSON><PERSON>, <PERSON><PERSON>)]
pub struct EncryptionVolumeStatus {
    pub volume: String,
    pub conversion_status: Option<String>,
    pub percentage_encrypted: Option<String>,
    pub encryption_method: Option<String>,
    pub protection_status: Option<String>,
    pub lock_status: Option<String>,
    pub identification_field: Option<String>,
    pub automatic_unlock: Option<String>,
    pub key_protectors: Vec<String>,
}

impl EncryptionVolumeStatus {
    pub fn get_all() -> Result<Vec<Self>> {
        let output = std::process::Command::new("manage-bde.exe")
            .arg("-status")
            .output()
            .unwrap();
        if output.status.success() == false {
            anyhow::bail!("Failed to get current status of Bitlocker Volumes");
        }
        let output = String::from_utf8_lossy(&output.stdout);
        Ok(Self::parse_manage_bde_status(&output))
    }

    pub fn parse_manage_bde_status(output: &str) -> Vec<EncryptionVolumeStatus> {
        let mut volumes = Vec::new();
        let mut current = EncryptionVolumeStatus::default();
        let mut in_key_protectors = false;

        for line in output.lines() {
            let trimmed = line.trim();

            // Detect start of new volume
            if trimmed.starts_with("Volume ") {
                // If previous volume exists, push it
                if !current.volume.is_empty() {
                    volumes.push(current);
                    current = EncryptionVolumeStatus::default();
                }

                // Extract text after "Volume "
                let rest = trimmed.trim_start_matches("Volume ").trim();

                // rest may be:
                // "C:"
                // "C:\"
                // "C: [OS Volume]"
                // "C:\ [OS Volume]"

                let volume = rest
                    .split_whitespace() // splits at space before comments
                    .next() // take "C:" or "C:\"
                    .unwrap_or("");

                current.volume = volume.to_string();
                in_key_protectors = false;
                continue;
            }

            // Detect key protector section
            if trimmed.starts_with("Key Protectors") {
                in_key_protectors = true;
                continue;
            }

            // Inside key protector list
            if in_key_protectors {
                if trimmed.starts_with("    ") {
                    current.key_protectors.push(trimmed.trim().to_string());
                    continue;
                } else {
                    in_key_protectors = false;
                }
            }

            // Parse key/value lines
            if let Some((key, value)) = trimmed.split_once(':') {
                let key = key.trim();
                let value = value.trim().to_string();

                match key {
                    "Conversion Status" => current.conversion_status = Some(value),
                    "Percentage Encrypted" => current.percentage_encrypted = Some(value),
                    "Encryption Method" => current.encryption_method = Some(value),
                    "Protection Status" => current.protection_status = Some(value),
                    "Lock Status" => current.lock_status = Some(value),
                    "Identification Field" => current.identification_field = Some(value),
                    "Automatic Unlock" => current.automatic_unlock = Some(value),
                    _ => {}
                }
            }
        }

        if !current.volume.is_empty() {
            debug!("Pushing volume: {:?}", current);
            volumes.push(current);
        }

        volumes
    }
}
